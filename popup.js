// DOM 元素引用
let elements = {};

// 服务商配置缓存
let providerConfigs = {};

// Chrome API兼容性检查
const isExtensionEnvironment = typeof chrome !== 'undefined' && chrome.storage;

document.addEventListener('DOMContentLoaded', async () => {
  // 初始化DOM元素引用
  initializeElements();

  // 初始化服务商配置
  await initializeProviderConfigs();

  // 设置事件监听器
  setupEventListeners();

  // 加载已保存的设置
  await loadSavedSettings();

  // 确保按钮状态正确初始化
  setTimeout(() => {
    validateInputs();
  }, 200);
});

/**
 * 环境兼容性的存储操作
 */
const storageAdapter = {
  async get(keys) {
    if (isExtensionEnvironment) {
      return new Promise(resolve => {
        chrome.storage.sync.get(keys, resolve);
      });
    } else {
      // 在非扩展环境中使用 localStorage 模拟
      const result = {};
      const keyArray = Array.isArray(keys) ? keys : [keys];
      keyArray.forEach(key => {
        const value = localStorage.getItem(key);
        if (value) {
          try {
            result[key] = JSON.parse(value);
          } catch {
            result[key] = value;
          }
        }
      });
      return result;
    }
  },

  async set(items) {
    if (isExtensionEnvironment) {
      return new Promise(resolve => {
        chrome.storage.sync.set(items, resolve);
      });
    } else {
      // 在非扩展环境中使用 localStorage 模拟
      Object.entries(items).forEach(([key, value]) => {
        localStorage.setItem(key, JSON.stringify(value));
      });
      return Promise.resolve();
    }
  }
};

/**
 * 初始化DOM元素引用
 */
function initializeElements() {
  elements = {
    // AI配置元素
    aiEnabled: document.getElementById('ai-enabled'),
    aiProvider: document.getElementById('ai-provider'),
    aiApiKey: document.getElementById('ai-api-key'),
    aiModel: document.getElementById('ai-model'),
    aiTemperature: document.getElementById('ai-temperature'),
    aiMaxTokens: document.getElementById('ai-max-tokens'),
    // providerInfo: document.getElementById('provider-info'), // 已注释掉
    advancedToggle: document.getElementById('advanced-toggle'),
    advancedContent: document.getElementById('advanced-content'),

    // AI区域折叠
    aiSectionToggle: document.getElementById('ai-section-toggle'),
    aiConfigSection: document.getElementById('ai-config-section'),

    // Flomo配置元素
    apiUrl: document.getElementById('api-url'),

    // 其他设置元素
    autoClose: document.getElementById('auto-close'),

    // 控制元素
    saveButton: document.getElementById('save-button'),
    testButton: document.getElementById('test-button'),
    statusMessage: document.getElementById('status-message'),
    testStatus: document.getElementById('test-status')
  };

  // 检查关键元素是否存在
  const missingElements = Object.entries(elements)
    .filter(([name, element]) => !element)
    .map(([name]) => name);

  if (missingElements.length > 0) {
    console.warn('⚠️ 以下元素未找到:', missingElements);
  }

  return elements;
}

/**
 * 初始化服务商配置
 */
async function initializeProviderConfigs() {
  try {
    providerConfigs = CONFIG_SCHEMA.ai.providers;
    console.log('✅ 服务商配置已加载', providerConfigs);
  } catch (error) {
    console.error('❌ 加载服务商配置失败:', error);
    showStatus('服务商配置加载失败', 'error');
  }
}

/**
 * 设置事件监听器
 */
function setupEventListeners() {
  // AI功能开关变化
  elements.aiEnabled.addEventListener('change', onAIEnabledChange);

  // AI服务商选择变化
  elements.aiProvider.addEventListener('change', onProviderChange);

  // API密钥输入变化
  elements.aiApiKey.addEventListener('input', onApiKeyInput);

  // 模型输入变化
  elements.aiModel.addEventListener('input', validateInputs);

  // AI区域折叠/展开
  elements.aiSectionToggle.addEventListener('click', toggleAISection);

  // 高级设置展开/折叠（仅在元素存在且可见时）
  if (elements.advancedToggle && elements.advancedToggle.offsetParent !== null) {
    elements.advancedToggle.addEventListener('click', toggleAdvancedSettings);
  }

  // 保存按钮
  elements.saveButton.addEventListener('click', saveSettings);

  // 测试连接按钮
  elements.testButton.addEventListener('click', testConnection);

  // 实时验证输入
  elements.apiUrl.addEventListener('input', onFlomoApiUrlChange);
  elements.aiApiKey.addEventListener('input', validateInputs);
}

/**
 * Flomo API URL变化处理
 */
async function onFlomoApiUrlChange() {
  // 先执行验证
  validateInputs();

  // 防抖处理，避免频繁同步
  if (onFlomoApiUrlChange.timeout) {
    clearTimeout(onFlomoApiUrlChange.timeout);
  }

  onFlomoApiUrlChange.timeout = setTimeout(async () => {
    const apiUrl = elements.apiUrl.value.trim();
    console.log('🔄 Flomo API URL变化:', apiUrl);

    try {
      // 保存到存储
      if (isExtensionEnvironment) {
        await chrome.storage.sync.set({ flomoApiUrl: apiUrl });
      } else {
        localStorage.setItem('flomoApiUrl', apiUrl);
      }

      // 通知侧边栏更新
      await notifySidepanelUpdate();
      console.log('✅ Flomo API URL已实时同步');

    } catch (error) {
      console.error('❌ Flomo API URL同步失败:', error);
    }
  }, 500); // 500ms防抖
}

/**
 * AI功能开关变化处理
 */
async function onAIEnabledChange() {
  const isEnabled = elements.aiEnabled.checked;
  console.log('🔄 AI功能开关状态变化:', isEnabled);

  // 保存开关状态到存储
  try {
    if (isExtensionEnvironment) {
      await chrome.storage.sync.set({ aiEnabled: isEnabled });
    } else {
      localStorage.setItem('aiEnabled', JSON.stringify(isEnabled));
    }
    console.log('✅ AI功能开关状态已保存:', isEnabled);

    // 通知侧边栏更新AI按钮显示状态
    await notifySidepanelUpdate();

  } catch (error) {
    console.error('❌ 保存AI功能开关状态失败:', error);
    showStatus('保存设置失败', 'error');
  }

  // 重新验证输入状态，更新测试按钮的显示和状态
  validateInputs();
}

/**
 * 通知侧边栏更新显示状态
 */
async function notifySidepanelUpdate() {
  try {
    if (isExtensionEnvironment) {
      // 清除统一配置管理器的缓存，确保侧边栏获取最新配置
      try {
        // 动态导入配置管理器并清除缓存
        const { configManager } = await import('./src/config/config-manager.js');
        if (configManager && configManager.clearCache) {
          configManager.clearCache();
          console.log('🔄 已清除配置管理器缓存');
        }
      } catch (error) {
        console.warn('⚠️ 清除配置缓存失败:', error);
      }

      // 发送消息给所有标签页的侧边栏
      const tabs = await chrome.tabs.query({});
      for (const tab of tabs) {
        try {
          await chrome.tabs.sendMessage(tab.id, {
            type: 'CONFIG_UPDATED',
            data: {
              // AI配置
              aiEnabled: elements.aiEnabled.checked,
              aiProvider: elements.aiProvider.value,
              aiApiKey: elements.aiApiKey.value,
              aiModel: elements.aiModel.value,
              // Flomo配置
              flomoApiUrl: elements.apiUrl.value.trim(),
              // 其他配置
              autoClose: elements.autoClose.checked,
              // 添加时间戳确保消息唯一性
              timestamp: Date.now()
            }
          });
        } catch (error) {
          // 忽略无法发送消息的标签页（如chrome://页面）
        }
      }
      console.log('📡 已通知侧边栏更新配置 (AI + Flomo)');
    }
  } catch (error) {
    console.error('❌ 通知侧边栏更新失败:', error);
  }
}

/**
 * 服务商选择变化处理
 */
async function onProviderChange() {
  const provider = elements.aiProvider.value;

  if (provider && providerConfigs[provider]) {
    const config = providerConfigs[provider];

    // 显示服务商信息（已移除provider-info元素）
    // elements.providerInfo.innerHTML = `
    //   <strong>${config.name}</strong><br>
    //   ${config.description}<br>
    //   <small>常用模型: ${config.models.slice(0, 3).join(', ')}${config.models.length > 3 ? '...' : ''}</small>
    // `;
    // elements.providerInfo.style.display = 'block';

    // 更新API密钥占位符
    elements.aiApiKey.placeholder = `请输入您的 ${config.name} API密钥`;

    // 更新模型输入框占位符和提示
    updateModelInput(config);

    // 启用模型输入
    elements.aiModel.disabled = false;

    // 加载对应服务商的API密钥
    await loadProviderApiKey(provider);

    // 验证当前API密钥
    validateApiKey();
  } else {
    // 清空显示（已移除provider-info元素）
    // elements.providerInfo.style.display = 'none';
    elements.aiModel.value = '';
    elements.aiModel.placeholder = '请先选择服务商';
    elements.aiModel.disabled = true;

    // 清空API密钥输入框和占位符
    elements.aiApiKey.value = '';
    elements.aiApiKey.placeholder = '请先选择服务商';
    elements.aiApiKey.style.borderColor = '#ddd';
  }

  validateInputs();
}

/**
 * 更新模型输入框
 */
function updateModelInput(config) {
  // 设置占位符，显示第一个推荐模型
  const firstModel = config.models[0] || '';
  elements.aiModel.placeholder = `请输入模型名称，如：${firstModel}`;

  // 清空当前值（如果需要的话）
  // elements.aiModel.value = '';
}

/**
 * API密钥输入处理
 */
function onApiKeyInput() {
  validateApiKey();
  validateInputs();
}

/**
 * 验证API密钥格式
 */
function validateApiKey() {
  const provider = elements.aiProvider.value;
  const apiKey = elements.aiApiKey.value.trim();

  if (!provider || !apiKey) {
    return false;
  }

  const config = providerConfigs[provider];
  if (!config) {
    return false;
  }

  const isValid = config.keyFormat.test(apiKey);

  // 更新输入框样式
  if (apiKey.length > 0) {
    elements.aiApiKey.style.borderColor = isValid ? '#34a853' : '#ea4335';
  } else {
    elements.aiApiKey.style.borderColor = '#ddd';
  }

  return isValid;
}

/**
 * 切换AI区域显示/隐藏
 */
function toggleAISection() {
  const content = elements.aiConfigSection;
  const toggle = elements.aiSectionToggle;
  const icon = toggle.querySelector('.toggle-icon');

  if (content.style.display === 'none' || content.style.display === '') {
    content.style.display = 'block';
    content.classList.add('expanded');
    icon.classList.add('expanded');
  } else {
    content.style.display = 'none';
    content.classList.remove('expanded');
    icon.classList.remove('expanded');
  }

  // 重新验证输入状态，这会更新测试按钮的可见性
  validateInputs();
}

/**
 * 切换高级设置显示
 */
function toggleAdvancedSettings() {
  // 检查元素是否存在且可见
  if (!elements.advancedContent || !elements.advancedToggle ||
    elements.advancedToggle.offsetParent === null) {
    return;
  }

  const content = elements.advancedContent;
  const icon = elements.advancedToggle.querySelector('.toggle-icon');

  if (!content || !icon) {
    return;
  }

  if (content.classList.contains('show')) {
    content.classList.remove('show');
    icon.classList.remove('open');
  } else {
    content.classList.add('show');
    icon.classList.add('open');
  }
}

/**
 * 验证所有输入
 */
function validateInputs() {
  const apiUrl = elements.apiUrl.value.trim();
  const provider = elements.aiProvider.value;
  const apiKey = elements.aiApiKey.value.trim();
  const model = elements.aiModel.value.trim();

  // 检查用户是否尝试配置AI
  const isConfiguringAI = !!provider || !!apiKey || !!model;
  // 检查用户是否尝试配置Flomo
  const isConfiguringFlomo = !!apiUrl;

  // AI配置完整性检查
  const hasCompleteAIConfig = provider && apiKey && model;
  const isValidAIConfig = hasCompleteAIConfig && validateApiKey();

  // Flomo配置有效性检查
  const isValidFlomoConfig = apiUrl && isValidUrl(apiUrl);

  // 判断是否可以保存：
  // 1. 允许保存任何有效的配置组合，包括空配置
  // 2. 如果输入了配置，则必须格式正确
  let canSave = true; // 默认允许保存

  // Flomo配置验证：如果输入了就必须格式正确
  const flomoConfigOk = !isConfiguringFlomo || (isConfiguringFlomo && isValidFlomoConfig);

  // AI配置验证：如果输入了就必须完整且有效
  const aiConfigOk = !isConfiguringAI || (isConfiguringAI && isValidAIConfig);

  // 只要输入的配置都是有效的就可以保存（允许空配置）
  canSave = flomoConfigOk && aiConfigOk;

  // 更新保存按钮状态
  elements.saveButton.disabled = !canSave;

  // 更新测试按钮状态和可见性
  const aiSectionExpanded = elements.aiConfigSection.style.display !== 'none';
  const aiEnabled = elements.aiEnabled.checked;
  const canTest = hasCompleteAIConfig && isValidAIConfig && aiEnabled;

  // 只有在AI区域展开且AI功能启用时才显示测试按钮
  const testSection = document.querySelector('.ai-test-section');
  if (testSection) {
    if (aiSectionExpanded && aiEnabled) {
      testSection.style.display = 'block';
      elements.testButton.disabled = !canTest;
    } else {
      testSection.style.display = 'none';
    }
  }

  return canSave;
}

/**
 * URL验证
 */
function isValidUrl(string) {
  try {
    const url = new URL(string);
    return url.protocol === "https:";
  } catch (_) {
    return false;
  }
}

/**
 * 加载已保存的设置
 */
async function loadSavedSettings() {
  try {
    // 环境检测提示
    if (!isExtensionEnvironment) {
      console.log('📝 在非扩展环境中运行，使用 localStorage 模拟存储');
      showStatus('演示模式：配置将保存到本地存储', 'info');
    }

    // 加载Flomo配置
    const result = await storageAdapter.get([
      'flomoApiUrl',
      'sidepanelConfig',
      'aiConfig',
      'aiEnabled',
      'providerConfigs'
    ]);

    // 设置Flomo API URL
    if (result.flomoApiUrl) {
      elements.apiUrl.value = result.flomoApiUrl;
    }

    // 设置侧边栏配置
    const sidepanelConfig = {
      autoClose: true,
      ...(result.sidepanelConfig || {})
    };
    elements.autoClose.checked = sidepanelConfig.autoClose;

    // 设置AI功能开关状态（默认开启）
    const aiEnabled = result.aiEnabled !== undefined ? result.aiEnabled : true;
    elements.aiEnabled.checked = aiEnabled;
    console.log('🔄 加载AI功能开关状态:', aiEnabled);

    // 设置AI配置
    const aiConfig = {
      provider: 'openrouter',
      model: 'deepseek/deepseek-chat-v3-0324:free',
      temperature: 0.7,
      maxTokens: 1000,
      ...(result.aiConfig || {})
    };

    // 仅在高级设置元素存在时设置值
    if (elements.aiTemperature) {
      elements.aiTemperature.value = aiConfig.temperature;
    }
    if (elements.aiMaxTokens) {
      elements.aiMaxTokens.value = aiConfig.maxTokens;
    }

    // 首先设置服务商，这会触发onProviderChange并加载对应的API密钥
    elements.aiProvider.value = aiConfig.provider;

    // 等待服务商变化处理完成（包括API密钥加载）
    if (aiConfig.provider) {
      await onProviderChange();

      // 直接设置模型到输入框
      setTimeout(() => {
        elements.aiModel.value = aiConfig.model;
        validateInputs(); // 验证输入
      }, 100);
    } else {
      // 即使没有AI配置，也要验证输入状态
      setTimeout(() => {
        validateInputs();
      }, 100);
    }

    console.log('✅ 设置加载完成');

  } catch (error) {
    console.error('加载设置失败:', error);
    showStatus('加载设置失败', 'error');
    // 即使加载失败，也要验证输入状态
    validateInputs();
  }
}

/**
 * 保存设置
 */
async function saveSettings() {
  try {
    const provider = elements.aiProvider.value;
    const apiKey = elements.aiApiKey.value.trim();
    const model = elements.aiModel.value;
    const apiUrl = elements.apiUrl.value.trim();
    const autoClose = elements.autoClose.checked;
    const aiEnabled = elements.aiEnabled.checked;
    // 使用默认值或从UI元素读取值（如果存在）
    const temperature = elements.aiTemperature ? parseFloat(elements.aiTemperature.value) : 0.7;
    const maxTokens = elements.aiMaxTokens ? parseInt(elements.aiMaxTokens.value) : 1000;

    // 验证输入
    if (!validateInputs()) {
      showStatus('请检查输入的配置信息', 'error');
      return;
    }

    // 准备配置数据
    const aiConfig = {
      provider,
      model,
      temperature,
      maxTokens
    };

    const sidepanelConfig = {
      autoClose,
      autoCloseDelay: 1000
    };

    // 获取现有的provider配置
    const existingData = await storageAdapter.get('providerConfigs');
    const providerConfigs = existingData.providerConfigs || {};

    // 更新当前provider的API密钥和模型选择
    if (provider && apiKey && model) {
      if (!providerConfigs[provider]) {
        providerConfigs[provider] = {};
      }
      providerConfigs[provider].apiKey = apiKey;
      providerConfigs[provider].model = model;

      console.log(`✅ 保存 ${provider} 配置 - 密钥: ${apiKey.substring(0, 4)}..., 模型: ${model}`);
    }

    // 保存所有配置
    await storageAdapter.set({
      flomoApiUrl: apiUrl,
      sidepanelConfig,
      aiConfig,
      aiEnabled,
      providerConfigs
    });

    // 获取服务商名称用于提示
    const providerName = provider ? (CONFIG_SCHEMA.ai.providers[provider]?.name || provider) : '';

    let successMessage;
    if (provider && apiUrl) {
      successMessage = `${providerName} 和 Flomo 配置已保存`;
    } else if (provider) {
      successMessage = `${providerName} 配置已保存`;
    } else if (apiUrl) {
      successMessage = 'Flomo 配置已保存';
    } else {
      successMessage = '配置已保存';
    }

    if (!isExtensionEnvironment) {
      successMessage += '到本地存储';
    }

    showStatus(successMessage, 'success');
    console.log(`✅ 配置保存完成`);

    // 通知侧边栏更新AI配置
    await notifySidepanelUpdate();

    // 在扩展环境中延迟关闭窗口
    if (isExtensionEnvironment) {
      setTimeout(() => {
        window.close();
      }, 1000);
    }

  } catch (error) {
    console.error('保存设置失败:', error);
    showStatus('保存设置失败: ' + error.message, 'error');
  }
}

/**
 * 显示状态消息
 */
function showStatus(message, type) {
  elements.statusMessage.textContent = message;
  elements.statusMessage.className = `status ${type}`;
  elements.statusMessage.style.display = 'block';

  if (type === 'success' || type === 'info') {
    setTimeout(() => {
      elements.statusMessage.style.display = 'none';
    }, type === 'info' ? 3000 : 2000);
  }
}

/**
 * 显示测试状态消息
 */
function showTestStatus(message, type = 'testing') {
  elements.testStatus.textContent = message;
  elements.testStatus.className = `test-status ${type}`;
  elements.testStatus.style.display = 'block';

  // 如果不是测试中状态，5秒后自动隐藏
  if (type !== 'testing') {
    setTimeout(() => {
      elements.testStatus.style.display = 'none';
    }, 5000);
  }
}

/**
 * 加载指定服务商的API密钥和模型选择
 */
async function loadProviderApiKey(provider) {
  try {
    const result = await storageAdapter.get('providerConfigs');
    const providerConfigs = result.providerConfigs || {};
    const providerName = CONFIG_SCHEMA.ai.providers[provider]?.name || provider;

    // 设置对应服务商的API密钥
    if (providerConfigs[provider] && providerConfigs[provider].apiKey) {
      const apiKey = providerConfigs[provider].apiKey;
      elements.aiApiKey.value = apiKey;

      // 显示密钥的前4位和后4位用于确认（不暴露完整密钥）
      const maskedKey = apiKey.length > 8
        ? `${apiKey.substring(0, 4)}...${apiKey.substring(apiKey.length - 4)}`
        : `${apiKey.substring(0, 2)}***`;

      console.log(`✅ 已加载 ${providerName} 的API密钥 (${maskedKey})`);
    } else {
      elements.aiApiKey.value = '';
      console.log(`📝 ${providerName} 尚未配置API密钥`);
    }

    // 设置对应服务商的模型选择
    if (providerConfigs[provider] && providerConfigs[provider].model) {
      const savedModel = providerConfigs[provider].model;
      // 直接设置模型值到输入框
      elements.aiModel.value = savedModel;
      console.log(`✅ 已加载 ${providerName} 的模型选择: ${savedModel}`);
    } else {
      // 清空模型输入框
      elements.aiModel.value = '';
      console.log(`📝 ${providerName} 尚未选择模型`);
    }

    // 重置边框样式
    elements.aiApiKey.style.borderColor = '#ddd';

  } catch (error) {
    console.error('加载配置失败:', error);
    elements.aiApiKey.value = '';
    elements.aiModel.value = '';
  }
}

/**
 * 测试AI API连接
 */
async function testConnection() {
  const provider = elements.aiProvider.value;
  const apiKey = elements.aiApiKey.value.trim();
  const model = elements.aiModel.value.trim();

  if (!provider || !apiKey || !model) {
    showTestStatus('请先完整填写AI配置信息', 'error');
    return;
  }

  // 获取提供商配置
  const config = providerConfigs[provider];
  if (!config) {
    showTestStatus('不支持的AI提供商', 'error');
    return;
  }

  // 显示测试中状态
  showTestStatus('正在测试连接...', 'testing');
  elements.testButton.disabled = true;
  elements.testButton.classList.add('loading');

  // 更新按钮图标为加载状态
  const iconElement = elements.testButton.querySelector('.btn-test-icon');
  if (iconElement) {
    iconElement.textContent = '⟳';
  }

  try {
    // 构建API请求
    const baseUrl = config.baseUrl;
    const apiUrl = `${baseUrl}/chat/completions`;

    // 创建测试请求
    const testMessage = {
      model: model,
      messages: [
        {
          role: 'user',
          content: 'Hello! This is a connection test. Please respond with "Connection successful".'
        }
      ],
      max_tokens: 50,
      temperature: 0.1
    };

    // 设置请求头
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    };

    // 特殊处理不同提供商的请求头
    if (provider === 'anthropic') {
      headers['anthropic-version'] = '2023-06-01';
    }

    // 发送测试请求，设置10秒超时
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(testMessage),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      let errorMessage = `API请求失败 (${response.status})`;

      try {
        const errorData = await response.json();
        if (errorData.error) {
          if (errorData.error.message) {
            errorMessage = errorData.error.message;
          } else if (typeof errorData.error === 'string') {
            errorMessage = errorData.error;
          }
        }
      } catch (e) {
        // 无法解析错误响应，使用默认错误消息
      }

      // 根据状态码提供具体建议
      if (response.status === 401) {
        errorMessage = 'API密钥无效，请检查密钥是否正确';
      } else if (response.status === 403) {
        errorMessage = 'API密钥权限不足或账户余额不足';
      } else if (response.status === 404) {
        errorMessage = '模型不存在或API端点错误';
      } else if (response.status === 429) {
        errorMessage = '请求频率过高，请稍后再试';
      }

      showTestStatus(`❌ ${errorMessage}`, 'error');

      // 添加错误状态视觉反馈
      elements.testButton.classList.add('error');
      const iconElement = elements.testButton.querySelector('.btn-test-icon');
      if (iconElement) {
        iconElement.textContent = '✗';
      }

      // 2秒后恢复原始状态
      setTimeout(() => {
        elements.testButton.classList.remove('error');
        if (iconElement) {
          iconElement.textContent = '⚡';
        }
      }, 2000);

      return;
    }

    // 解析响应
    const data = await response.json();

    // 验证响应格式
    if (data.choices && data.choices.length > 0) {
      const responseText = data.choices[0].message?.content || data.choices[0].text || '';
      showTestStatus(`✅ 连接成功！模型响应正常`, 'success');
      console.log('✅ API测试成功，响应:', responseText.substring(0, 100));

      // 添加成功状态视觉反馈
      elements.testButton.classList.add('success');
      const iconElement = elements.testButton.querySelector('.btn-test-icon');
      if (iconElement) {
        iconElement.textContent = '✓';
      }

      // 2秒后恢复原始状态
      setTimeout(() => {
        elements.testButton.classList.remove('success');
        if (iconElement) {
          iconElement.textContent = '⚡';
        }
      }, 2000);

    } else {
      showTestStatus('⚠️ 连接成功但响应格式异常', 'warning');
    }

  } catch (error) {
    let errorMessage = '连接测试失败';

    if (error.name === 'AbortError') {
      errorMessage = '请求超时，请检查网络连接';
    } else if (error.message.includes('Failed to fetch')) {
      errorMessage = '网络连接失败，请检查网络设置';
    } else if (error.message.includes('CORS')) {
      errorMessage = 'CORS错误，可能是API端点配置问题';
    } else {
      errorMessage = error.message || '未知错误';
    }

    showTestStatus(`❌ ${errorMessage}`, 'error');
    console.error('API测试失败:', error);

    // 添加错误状态视觉反馈
    elements.testButton.classList.add('error');
    const iconElement = elements.testButton.querySelector('.btn-test-icon');
    if (iconElement) {
      iconElement.textContent = '✗';
    }

    // 2秒后恢复原始状态
    setTimeout(() => {
      elements.testButton.classList.remove('error');
      if (iconElement) {
        iconElement.textContent = '⚡';
      }
    }, 2000);

  } finally {
    // 恢复按钮状态
    elements.testButton.disabled = false;
    elements.testButton.classList.remove('loading');

    // 重新验证输入以确保按钮状态正确
    setTimeout(() => {
      validateInputs();
    }, 100);
  }
}


