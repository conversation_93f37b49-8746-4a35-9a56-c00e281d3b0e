<!DOCTYPE html>
<html>

<head>
  <title>测试按钮样式美化对比</title>
  <meta charset="UTF-8">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 13px;
      margin: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #333;
      min-height: 100vh;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    h1 {
      text-align: center;
      color: white;
      margin-bottom: 30px;
      font-size: 28px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .comparison-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 30px;
      margin-bottom: 40px;
    }

    .demo-section {
      background: white;
      border-radius: 16px;
      padding: 25px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
    }

    .demo-title {
      font-size: 20px;
      font-weight: 600;
      text-align: center;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 2px solid;
    }

    .old-design {
      border-color: #f44336;
      color: #f44336;
    }

    .new-design {
      border-color: #4caf50;
      color: #4caf50;
    }

    .ai-test-section {
      margin-top: 16px;
      padding-top: 12px;
      border-top: 1px solid #f0f0f0;
      text-align: center;
    }

    /* 原始按钮样式 */
    .btn-test-old {
      width: auto;
      padding: 6px 16px;
      background: #f8f9fa;
      color: #5f6368;
      border: 1px solid #dadce0;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      margin-bottom: 8px;
      transition: all 0.2s ease;
    }

    .btn-test-old:hover {
      background: #e8f0fe;
      color: #1a73e8;
      border-color: #1a73e8;
    }

    .btn-test-old:disabled {
      background: #f5f5f5;
      color: #9aa0a6;
      border-color: #e8eaed;
      cursor: not-allowed;
    }

    /* 新的现代化按钮样式 */
    .btn-test {
      position: relative;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      width: auto;
      padding: 8px 16px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 12px;
      font-weight: 500;
      cursor: pointer;
      margin-bottom: 8px;
      box-shadow: 0 2px 4px rgba(102, 126, 234, 0.25);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      overflow: hidden;
    }

    .btn-test::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .btn-test:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }

    .btn-test:hover:not(:disabled)::before {
      opacity: 1;
    }

    .btn-test:active:not(:disabled) {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
    }

    .btn-test:disabled {
      background: linear-gradient(135deg, #e0e0e0 0%, #bdbdbd 100%);
      color: #9e9e9e;
      cursor: not-allowed;
      transform: none;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .btn-test:disabled::before {
      display: none;
    }

    .btn-test-icon {
      font-size: 14px;
      line-height: 1;
    }

    /* 加载状态 */
    .btn-test.loading {
      pointer-events: none;
    }

    .btn-test.loading .btn-test-icon {
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }

    /* 成功状态 */
    .btn-test.success {
      background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
      animation: success-pulse 0.6s ease-out;
    }

    @keyframes success-pulse {
      0% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.05);
      }
      100% {
        transform: scale(1);
      }
    }

    /* 错误状态 */
    .btn-test.error {
      background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
      animation: error-shake 0.5s ease-out;
    }

    @keyframes error-shake {
      0%, 100% {
        transform: translateX(0);
      }
      25% {
        transform: translateX(-2px);
      }
      75% {
        transform: translateX(2px);
      }
    }

    /* 状态演示区域 */
    .states-demo {
      background: white;
      border-radius: 16px;
      padding: 25px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      margin-top: 30px;
    }

    .states-demo h2 {
      text-align: center;
      color: #333;
      margin-bottom: 20px;
      font-size: 20px;
    }

    .states-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }

    .state-item {
      text-align: center;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .state-label {
      font-size: 12px;
      color: #666;
      margin-bottom: 10px;
      font-weight: 500;
    }

    .improvements {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 16px;
      padding: 25px;
      margin-top: 30px;
      backdrop-filter: blur(10px);
    }

    .improvements h3 {
      color: #4caf50;
      margin-bottom: 15px;
      font-size: 18px;
      text-align: center;
    }

    .improvement-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 15px;
    }

    .improvement-item {
      display: flex;
      align-items: center;
      padding: 10px;
      background: #e8f5e8;
      border-radius: 8px;
      font-size: 12px;
      color: #2e7d32;
    }

    .improvement-item::before {
      content: "✓";
      margin-right: 10px;
      font-weight: bold;
      color: #4caf50;
      font-size: 14px;
    }

    .control-panel {
      text-align: center;
      margin: 20px 0;
    }

    .control-btn {
      margin: 5px;
      padding: 8px 16px;
      background: rgba(255, 255, 255, 0.2);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 6px;
      cursor: pointer;
      font-size: 12px;
      transition: all 0.2s ease;
    }

    .control-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  </style>
</head>

<body>
  <div class="container">
    <h1>🎨 测试按钮样式美化对比</h1>

    <div class="comparison-grid">
      <!-- 原始设计 -->
      <div class="demo-section">
        <div class="demo-title old-design">原始按钮设计</div>
        <div class="ai-test-section">
          <button class="btn-test-old">测试 AI API 连接</button>
          <div style="font-size: 11px; color: #666; margin-top: 8px;">
            简单的灰色按钮，缺乏视觉吸引力
          </div>
        </div>
      </div>

      <!-- 优化设计 -->
      <div class="demo-section">
        <div class="demo-title new-design">现代化按钮设计</div>
        <div class="ai-test-section">
          <button class="btn-test" id="modern-btn">
            <span class="btn-test-icon">⚡</span>
            <span>测试 AI API 连接</span>
          </button>
          <div style="font-size: 11px; color: #666; margin-top: 8px;">
            渐变背景 + 图标 + 动画效果
          </div>
        </div>
      </div>
    </div>

    <!-- 交互状态演示 -->
    <div class="states-demo">
      <h2>🎭 交互状态演示</h2>
      <div class="control-panel">
        <button class="control-btn" onclick="showState('default')">默认状态</button>
        <button class="control-btn" onclick="showState('loading')">加载状态</button>
        <button class="control-btn" onclick="showState('success')">成功状态</button>
        <button class="control-btn" onclick="showState('error')">错误状态</button>
        <button class="control-btn" onclick="showState('disabled')">禁用状态</button>
      </div>

      <div class="states-grid">
        <div class="state-item">
          <div class="state-label">默认状态</div>
          <button class="btn-test">
            <span class="btn-test-icon">⚡</span>
            <span>测试连接</span>
          </button>
        </div>

        <div class="state-item">
          <div class="state-label">加载状态</div>
          <button class="btn-test loading">
            <span class="btn-test-icon">⟳</span>
            <span>测试连接</span>
          </button>
        </div>

        <div class="state-item">
          <div class="state-label">成功状态</div>
          <button class="btn-test success">
            <span class="btn-test-icon">✓</span>
            <span>测试连接</span>
          </button>
        </div>

        <div class="state-item">
          <div class="state-label">错误状态</div>
          <button class="btn-test error">
            <span class="btn-test-icon">✗</span>
            <span>测试连接</span>
          </button>
        </div>

        <div class="state-item">
          <div class="state-label">禁用状态</div>
          <button class="btn-test" disabled>
            <span class="btn-test-icon">⚡</span>
            <span>测试连接</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 改进说明 -->
    <div class="improvements">
      <h3>🚀 视觉设计改进</h3>
      <div class="improvement-grid">
        <div class="improvement-item">采用现代化渐变背景设计</div>
        <div class="improvement-item">添加闪电图标增强视觉识别</div>
        <div class="improvement-item">实现平滑的悬停和点击动画</div>
        <div class="improvement-item">增加立体阴影效果</div>
        <div class="improvement-item">设计丰富的交互状态反馈</div>
        <div class="improvement-item">优化颜色对比度和可访问性</div>
        <div class="improvement-item">加载状态旋转动画</div>
        <div class="improvement-item">成功状态脉冲动画</div>
        <div class="improvement-item">错误状态震动动画</div>
        <div class="improvement-item">禁用状态灰度处理</div>
        <div class="improvement-item">使用 CSS3 高级特性</div>
        <div class="improvement-item">保持良好的键盘导航支持</div>
      </div>
    </div>
  </div>

  <script>
    function showState(state) {
      const btn = document.getElementById('modern-btn');
      const icon = btn.querySelector('.btn-test-icon');
      
      // 清除所有状态
      btn.classList.remove('loading', 'success', 'error');
      btn.disabled = false;
      
      switch (state) {
        case 'loading':
          btn.classList.add('loading');
          icon.textContent = '⟳';
          break;
        case 'success':
          btn.classList.add('success');
          icon.textContent = '✓';
          setTimeout(() => {
            btn.classList.remove('success');
            icon.textContent = '⚡';
          }, 2000);
          break;
        case 'error':
          btn.classList.add('error');
          icon.textContent = '✗';
          setTimeout(() => {
            btn.classList.remove('error');
            icon.textContent = '⚡';
          }, 2000);
          break;
        case 'disabled':
          btn.disabled = true;
          icon.textContent = '⚡';
          break;
        default:
          icon.textContent = '⚡';
      }
    }

    // 自动演示循环
    let demoIndex = 0;
    const states = ['default', 'loading', 'success', 'error', 'disabled'];
    
    function autoDemoCycle() {
      showState(states[demoIndex]);
      demoIndex = (demoIndex + 1) % states.length;
    }
    
    // 每3秒自动切换状态演示
    setInterval(autoDemoCycle, 3000);
  </script>
</body>

</html>
