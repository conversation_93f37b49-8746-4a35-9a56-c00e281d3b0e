<!DOCTYPE html>
<html>

<head>
  <title>布局对比 - 原始 vs 紧凑</title>
  <meta charset="UTF-8">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 20px;
      background: #f0f0f0;
    }

    .comparison-container {
      display: flex;
      gap: 30px;
      justify-content: center;
      align-items: flex-start;
    }

    .layout-demo {
      position: relative;
    }

    .layout-title {
      text-align: center;
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 15px;
      padding: 10px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .original {
      color: #d93025;
    }

    .compact {
      color: #137333;
    }

    /* 原始布局样式 */
    .original-popup {
      font-size: 14px;
      padding: 20px;
      width: 400px;
      background: #f8f9fa;
    }

    .original-popup .container {
      background: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .original-popup h1 {
      margin: 0 0 20px 0;
      font-size: 18px;
      color: #333;
      text-align: center;
    }

    .original-popup .section {
      margin-bottom: 24px;
      border-bottom: 1px solid #f0f0f0;
      padding-bottom: 20px;
    }

    .original-popup .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
    }

    .original-popup .form-group {
      margin-bottom: 16px;
    }

    .original-popup label {
      display: block;
      margin-bottom: 6px;
      font-weight: 500;
      color: #555;
    }

    .original-popup input {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      box-sizing: border-box;
    }

    .original-popup .btn {
      width: 100%;
      padding: 12px;
      background: #1a73e8;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;
      margin-top: 10px;
    }

    /* 紧凑布局样式 */
    .compact-popup {
      font-size: 13px;
      padding: 12px;
      width: 380px;
      background: #f8f9fa;
    }

    .compact-popup .container {
      background: white;
      border-radius: 8px;
      padding: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .compact-popup h1 {
      margin: 0 0 12px 0;
      font-size: 16px;
      color: #333;
      text-align: center;
    }

    .compact-popup .section {
      margin-bottom: 16px;
      border-bottom: 1px solid #f0f0f0;
      padding-bottom: 12px;
    }

    .compact-popup .section-title {
      font-size: 15px;
      font-weight: 600;
      color: #333;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
    }

    .compact-popup .form-group {
      margin-bottom: 12px;
    }

    .compact-popup label {
      display: block;
      margin-bottom: 4px;
      font-weight: 500;
      color: #555;
    }

    .compact-popup input {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 13px;
      box-sizing: border-box;
    }

    .compact-popup .btn {
      width: 100%;
      padding: 8px 12px;
      background: #1a73e8;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 13px;
      cursor: pointer;
      margin-top: 8px;
    }

    .section-title::before {
      content: "";
      width: 4px;
      height: 16px;
      background: #1a73e8;
      margin-right: 8px;
      border-radius: 2px;
    }

    .stats {
      position: fixed;
      top: 20px;
      right: 20px;
      background: white;
      padding: 15px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      font-size: 14px;
      min-width: 200px;
    }

    .stats h3 {
      margin: 0 0 10px 0;
      color: #333;
    }

    .stat-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
    }

    .improvement {
      color: #137333;
      font-weight: 600;
    }
  </style>
</head>

<body>
  <div class="stats">
    <h3>优化效果统计</h3>
    <div class="stat-item">
      <span>宽度减少:</span>
      <span class="improvement">-20px (5%)</span>
    </div>
    <div class="stat-item">
      <span>字体大小:</span>
      <span class="improvement">-1px</span>
    </div>
    <div class="stat-item">
      <span>整体间距:</span>
      <span class="improvement">-20~30%</span>
    </div>
    <div class="stat-item">
      <span>预计高度减少:</span>
      <span class="improvement">~15-20%</span>
    </div>
  </div>

  <div class="comparison-container">
    <div class="layout-demo">
      <div class="layout-title original">原始布局 (400px)</div>
      <div class="original-popup">
        <div class="container">
          <h1>Save to Flomo - 设置</h1>

          <div class="section">
            <div class="section-title">Flomo 配置</div>
            <div class="form-group">
              <label>Flomo API 地址</label>
              <input type="text" placeholder="例如: https://flomoapp.com/api/...">
            </div>
          </div>

          <div class="section">
            <div class="section-title">高级功能设置</div>
            <div class="form-group">
              <label>AI 服务商</label>
              <input type="text" placeholder="请选择服务商">
            </div>
            <div class="form-group">
              <label>API 密钥</label>
              <input type="password" placeholder="请输入您的API密钥">
            </div>
          </div>

          <div class="section">
            <div class="section-title">其他设置</div>
            <div class="form-group">
              <label>
                <input type="checkbox" checked> 保存成功后自动关闭侧边栏
              </label>
            </div>
          </div>

          <button class="btn">保存设置</button>
        </div>
      </div>
    </div>

    <div class="layout-demo">
      <div class="layout-title compact">紧凑布局 (380px)</div>
      <div class="compact-popup">
        <div class="container">
          <h1>Save to Flomo - 设置</h1>

          <div class="section">
            <div class="section-title">Flomo 配置</div>
            <div class="form-group">
              <label>Flomo API 地址</label>
              <input type="text" placeholder="例如: https://flomoapp.com/api/...">
            </div>
          </div>

          <div class="section">
            <div class="section-title">高级功能设置</div>
            <div class="form-group">
              <label>AI 服务商</label>
              <input type="text" placeholder="请选择服务商">
            </div>
            <div class="form-group">
              <label>API 密钥</label>
              <input type="password" placeholder="请输入您的API密钥">
            </div>
          </div>

          <div class="section">
            <div class="section-title">其他设置</div>
            <div class="form-group">
              <label>
                <input type="checkbox" checked> 保存成功后自动关闭侧边栏
              </label>
            </div>
          </div>

          <button class="btn">保存设置</button>
        </div>
      </div>
    </div>
  </div>
</body>

</html>
