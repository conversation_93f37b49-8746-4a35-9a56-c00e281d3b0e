<!DOCTYPE html>
<html>

<head>
  <title>Save to Flomo - 设置</title>
  <meta charset="UTF-8">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 13px;
      margin: 0;
      padding: 12px;
      width: 380px;
      background: #f8f9fa;
    }

    .container {
      background: white;
      border-radius: 8px;
      padding: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    h1 {
      margin: 0 0 12px 0;
      font-size: 16px;
      color: #333;
      text-align: center;
    }

    .section {
      margin-bottom: 16px;
      border-bottom: 1px solid #f0f0f0;
      padding-bottom: 12px;
    }

    .section:last-child {
      border-bottom: none;
      margin-bottom: 0;
      padding-bottom: 0;
    }

    .section-title {
      font-size: 15px;
      font-weight: 600;
      color: #333;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
    }

    .section-title::before {
      content: "";
      width: 4px;
      height: 16px;
      background: #1a73e8;
      margin-right: 8px;
      border-radius: 2px;
    }

    .form-group {
      margin-bottom: 12px;
    }

    label {
      display: block;
      margin-bottom: 4px;
      font-weight: 500;
      color: #555;
    }

    input[type="text"],
    input[type="password"],
    select {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 13px;
      box-sizing: border-box;
    }

    input[type="text"]:focus,
    input[type="password"]:focus,
    select:focus {
      outline: none;
      border-color: #1a73e8;
      box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
    }

    select {
      cursor: pointer;
    }

    .checkbox-group {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
    }

    /* 现代化复选框样式 */
    .modern-checkbox {
      position: relative;
      display: inline-flex;
      align-items: center;
      cursor: pointer;
      font-size: 13px;
      user-select: none;
      font-weight: normal;
    }

    .modern-checkbox input {
      position: absolute;
      opacity: 0;
      cursor: pointer;
      height: 0;
      width: 0;
    }

    .modern-checkmark {
      position: relative;
      height: 18px;
      width: 18px;
      background-color: #fff;
      border: 2px solid #ddd;
      border-radius: 4px;
      margin-right: 10px;
      transition: all 0.3s ease;
      flex-shrink: 0;
    }

    .modern-checkbox:hover .modern-checkmark {
      border-color: #1a73e8;
      box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
      transform: scale(1.05);
    }

    .modern-checkbox input:checked~.modern-checkmark {
      background-color: #1a73e8;
      border-color: #1a73e8;
    }

    .modern-checkbox input:disabled~.modern-checkmark {
      background-color: #f5f5f5;
      border-color: #e0e0e0;
      cursor: not-allowed;
    }

    .modern-checkbox input:disabled {
      cursor: not-allowed;
    }

    .modern-checkbox.disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .modern-checkmark:after {
      content: "";
      position: absolute;
      display: none;
      left: 5px;
      top: 2px;
      width: 4px;
      height: 8px;
      border: solid white;
      border-width: 0 2px 2px 0;
      transform: rotate(45deg);
      animation: checkmark-appear 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .modern-checkbox input:checked~.modern-checkmark:after {
      display: block;
    }

    @keyframes checkmark-appear {
      0% {
        opacity: 0;
        transform: rotate(45deg) scale(0);
      }

      50% {
        opacity: 1;
        transform: rotate(45deg) scale(1.2);
      }

      100% {
        opacity: 1;
        transform: rotate(45deg) scale(1);
      }
    }

    /* 保持向后兼容的传统复选框样式 */
    .checkbox-group input[type="checkbox"] {
      width: auto;
      margin-right: 8px;
    }

    .checkbox-group label {
      margin-bottom: 0;
      font-weight: normal;
    }

    .advanced-settings {
      background: #f8f9fa;
      border-radius: 4px;
      padding: 12px;
      margin-top: 12px;
    }

    .advanced-title {
      font-size: 13px;
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;
      cursor: pointer;
      user-select: none;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .advanced-content {
      display: none;
    }

    .advanced-content.show {
      display: block;
    }

    .toggle-icon {
      transition: transform 0.2s;
    }

    .toggle-icon.open {
      transform: rotate(90deg);
    }

    .form-row {
      display: flex;
      gap: 12px;
    }

    .form-row .form-group {
      flex: 1;
    }

    .btn {
      width: 100%;
      padding: 8px 12px;
      background: #1a73e8;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 13px;
      cursor: pointer;
      margin-top: 8px;
    }

    .btn:hover {
      background: #1557b0;
    }

    .btn:disabled {
      background: #ccc;
      cursor: not-allowed;
    }

    .btn-secondary {
      background: #5f6368;
    }

    .btn-secondary:hover {
      background: #4a4d52;
    }

    .btn-secondary:disabled {
      background: #ccc;
    }

    .button-group {
      display: flex;
      gap: 8px;
      margin-top: 8px;
    }

    .button-group .btn {
      margin-top: 0;
      flex: 1;
    }

    /* 主要操作按钮区域 */
    .main-action {
      margin-top: 8px;
    }

    /* AI 测试区域样式 */
    .ai-test-section {
      margin-top: 16px;
      padding-top: 12px;
      border-top: 1px solid #f0f0f0;
    }

    .btn-test {
      width: auto;
      padding: 6px 16px;
      background: #f8f9fa;
      color: #5f6368;
      border: 1px solid #dadce0;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      margin-top: 0;
      margin-bottom: 8px;
      transition: all 0.2s ease;
    }

    .btn-test:hover:not(:disabled) {
      background: #e8f0fe;
      color: #1a73e8;
      border-color: #1a73e8;
    }

    .btn-test:disabled {
      background: #f5f5f5;
      color: #9aa0a6;
      border-color: #e8eaed;
      cursor: not-allowed;
    }

    .ai-test-section .test-status {
      margin-top: 6px;
      padding: 6px 10px;
      border-radius: 4px;
      font-size: 12px;
      display: none;
    }

    .loading {
      position: relative;
      pointer-events: none;
    }

    .loading::after {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      width: 16px;
      height: 16px;
      margin: -8px 0 0 -8px;
      border: 2px solid transparent;
      border-top: 2px solid #fff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    .status {
      margin-top: 8px;
      padding: 8px;
      border-radius: 4px;
      text-align: center;
      font-size: 12px;
      display: none;
    }

    .status.success {
      background: #e6f4ea;
      color: #137333;
      border: 1px solid #34a853;
    }

    .status.error {
      background: #fce8e6;
      color: #d93025;
      border: 1px solid #ea4335;
    }

    .status.info {
      background: #e8f0fe;
      color: #1a73e8;
      border: 1px solid #1a73e8;
    }

    .status.warning {
      background: #fef7e0;
      color: #ea8600;
      border: 1px solid #fbbc04;
    }

    .test-status {
      margin-top: 6px;
      padding: 6px 10px;
      border-radius: 4px;
      font-size: 12px;
      display: none;
    }

    .test-status.success {
      background: #e6f4ea;
      color: #137333;
      border: 1px solid #34a853;
    }

    .test-status.error {
      background: #fce8e6;
      color: #d93025;
      border: 1px solid #ea4335;
    }

    .test-status.testing {
      background: #e8f0fe;
      color: #1a73e8;
      border: 1px solid #1a73e8;
    }

    .info {
      background: #e8f0fe;
      border: 1px solid #1a73e8;
      border-radius: 4px;
      padding: 10px;
      margin-top: 12px;
      font-size: 12px;
      color: #1a73e8;
    }

    .provider-info {
      background: #f8f9fa;
      border-radius: 4px;
      padding: 6px 10px;
      margin-top: 6px;
      font-size: 12px;
      color: #666;
    }

    .api-key-hint {
      font-size: 12px;
      color: #666;
      margin-top: 4px;
    }

    /* 可折叠区域样式 */
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      padding: 6px 0;
      border-bottom: 1px solid #e0e0e0;
      margin-bottom: 6px;
    }

    .section-header:hover {
      background-color: #f5f5f5;
      margin: 0 -10px 6px -10px;
      padding: 6px 10px;
      border-radius: 4px;
    }

    .section-description {
      font-size: 12px;
      color: #666;
      margin-bottom: 12px;
      font-style: italic;
    }

    .toggle-icon {
      font-size: 12px;
      color: #666;
      transition: transform 0.3s ease;
    }

    .toggle-icon.expanded {
      transform: rotate(180deg);
    }

    .collapsible-content {
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .collapsible-content.expanded {
      display: block !important;
    }

    /* Toggle Switch 样式 */
    .toggle-switch {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
      padding: 8px 0;
      border-bottom: 1px solid #e0e0e0;
    }

    .toggle-switch-info {
      flex: 1;
    }

    .toggle-switch-label {
      font-weight: 600;
      color: #333;
      font-size: 13px;
      margin-bottom: 2px;
    }

    .toggle-switch-description {
      font-size: 12px;
      color: #666;
      line-height: 1.3;
    }

    .switch {
      position: relative;
      display: inline-block;
      width: 44px;
      height: 24px;
      flex-shrink: 0;
    }

    .switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .3s;
      border-radius: 24px;
    }

    .slider:before {
      position: absolute;
      content: "";
      height: 18px;
      width: 18px;
      left: 3px;
      bottom: 3px;
      background-color: white;
      transition: .3s;
      border-radius: 50%;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    input:checked+.slider {
      background-color: #4CAF50;
    }

    input:checked+.slider:before {
      transform: translateX(20px);
    }

    .slider:hover {
      box-shadow: 0 0 8px rgba(76, 175, 80, 0.3);
    }

    input:checked+.slider:hover {
      box-shadow: 0 0 8px rgba(76, 175, 80, 0.5);
    }
  </style>
</head>

<body>
  <div class="container">
    <h1>Save to Flomo - 设置</h1>

    <!-- Flomo配置部分 -->
    <div class="section">
      <div class="section-title">Flomo 配置</div>

      <div class="form-group">
        <label for="api-url">Flomo API 地址</label>
        <input type="text" id="api-url" placeholder="例如: https://flomoapp.com/api/...">
      </div>
    </div>

    <!-- 高级功能设置部分 -->
    <div class="section">
      <div class="section-header" id="ai-section-toggle">
        <div class="section-title">高级功能设置</div>
        <span class="toggle-icon">▼</span>
      </div>
      <div class="section-description">
        可选配置：启用智能标签生成和中英对照翻译功能
      </div>
      <div class="collapsible-content" id="ai-config-section" style="display: none;">
        <!-- AI功能开关 -->
        <div class="toggle-switch">
          <div class="toggle-switch-info">
            <div class="toggle-switch-label">启用AI功能</div>
            <div class="toggle-switch-description">开启后可使用智能标签生成和中英对照翻译功能</div>
          </div>
          <label class="switch">
            <input type="checkbox" id="ai-enabled" checked>
            <span class="slider"></span>
          </label>
        </div>

        <div class="form-group">
          <label for="ai-provider">AI 服务商</label>
          <select id="ai-provider">
            <option value="">请选择服务商</option>
            <option value="openrouter">OpenRouter</option>
            <option value="deepseek">DeepSeek</option>
            <option value="siliconflow">硅基流动</option>
            <option value="moonshot">Moonshot AI</option>
            <option value="doubao">豆包</option>
            <option value="openai">OpenAI</option>
            <option value="anthropic">Anthropic</option>
          </select>
          <!-- <div id="provider-info" class="provider-info" style="display: none;"></div> -->
        </div>

        <div class="form-group">
          <label for="ai-api-key">API 密钥</label>
          <input type="password" id="ai-api-key" placeholder="请输入您的API密钥">
          <div class="api-key-hint">请确保API密钥格式正确，密钥将安全存储在本地</div>
        </div>

        <div class="form-group">
          <label for="ai-model">模型</label>
          <input type="text" id="ai-model" placeholder="请输入模型名称，如：gpt-4o" disabled>
          <div class="model-hint" style="font-size: 12px; color: #666; margin-top: 4px;">
            请输入您要使用的模型名称，支持任意模型
          </div>
        </div>

        <!-- AI API 连接测试 -->
        <div class="ai-test-section">
          <button class="btn btn-test" id="test-button" disabled>测试 AI API 连接</button>
          <div id="test-status" class="test-status"></div>
        </div>

        <!-- 高级设置已隐藏，使用默认值 -->
        <div class="advanced-settings" style="display: none;">
          <div class="advanced-title" id="advanced-toggle">
            高级设置
            <span class="toggle-icon">▶</span>
          </div>
          <div class="advanced-content" id="advanced-content">
            <div class="form-row">
              <div class="form-group">
                <label for="ai-temperature">Temperature</label>
                <input type="number" id="ai-temperature" min="0" max="1" step="0.1" value="0.7">
              </div>
              <div class="form-group">
                <label for="ai-max-tokens">最大Token数</label>
                <input type="number" id="ai-max-tokens" min="100" max="10000" step="100" value="1000">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 其他设置 -->
    <div class="section">
      <div class="section-title">其他设置</div>

      <div class="toggle-switch">
        <div class="toggle-switch-info">
          <div class="toggle-switch-label">自动关闭侧边栏</div>
          <div class="toggle-switch-description">保存成功后自动关闭侧边栏</div>
        </div>
        <label class="switch">
          <input type="checkbox" id="auto-close" checked>
          <span class="slider"></span>
        </label>
      </div>
    </div>

    <div class="main-action">
      <button class="btn" id="save-button">保存设置</button>
    </div>
    <div id="status-message" class="status"></div>

    <div class="info">
      💡 配置完成后，您可以在网页上选择文本并使用右键菜单或侧边栏进行智能标签生成和保存到Flomo。
    </div>
  </div>

  <script src="popup-config.js"></script>
  <script src="popup.js"></script>
</body>

</html>