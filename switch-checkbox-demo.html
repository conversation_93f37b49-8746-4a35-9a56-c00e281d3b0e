<!DOCTYPE html>
<html>

<head>
  <title>开关样式复选框演示</title>
  <meta charset="UTF-8">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 13px;
      margin: 0;
      padding: 12px;
      background: #f8f9fa;
    }

    .demo-container {
      display: flex;
      gap: 30px;
      justify-content: center;
      align-items: flex-start;
      min-height: 100vh;
      padding: 20px;
    }

    .demo-section {
      background: white;
      border-radius: 8px;
      padding: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      width: 380px;
    }

    .demo-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      text-align: center;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 2px solid #1a73e8;
    }

    .original {
      border-color: #d93025;
      color: #d93025;
    }

    .improved {
      border-color: #137333;
      color: #137333;
    }

    h1 {
      margin: 0 0 12px 0;
      font-size: 16px;
      color: #333;
      text-align: center;
    }

    .section {
      margin-bottom: 16px;
      border-bottom: 1px solid #f0f0f0;
      padding-bottom: 12px;
    }

    .section:last-child {
      border-bottom: none;
      margin-bottom: 0;
      padding-bottom: 0;
    }

    .section-title {
      font-size: 15px;
      font-weight: 600;
      color: #333;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
    }

    .section-title::before {
      content: "";
      width: 4px;
      height: 16px;
      background: #1a73e8;
      margin-right: 8px;
      border-radius: 2px;
    }

    /* 原始复选框样式 */
    .checkbox-group {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
    }

    .checkbox-group input[type="checkbox"] {
      width: auto;
      margin-right: 8px;
    }

    .checkbox-group label {
      margin-bottom: 0;
      font-weight: normal;
    }

    /* Toggle Switch 样式 */
    .toggle-switch {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
      padding: 8px 0;
      border-bottom: 1px solid #e0e0e0;
    }

    .toggle-switch:last-child {
      border-bottom: none;
    }

    .toggle-switch-info {
      flex: 1;
    }

    .toggle-switch-label {
      font-weight: 600;
      color: #333;
      font-size: 13px;
      margin-bottom: 2px;
    }

    .toggle-switch-description {
      font-size: 12px;
      color: #666;
      line-height: 1.3;
    }

    .switch {
      position: relative;
      display: inline-block;
      width: 44px;
      height: 24px;
      flex-shrink: 0;
    }

    .switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .3s;
      border-radius: 24px;
    }

    .slider:before {
      position: absolute;
      content: "";
      height: 18px;
      width: 18px;
      left: 3px;
      bottom: 3px;
      background-color: white;
      transition: .3s;
      border-radius: 50%;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    input:checked+.slider {
      background-color: #4CAF50;
    }

    input:checked+.slider:before {
      transform: translateX(20px);
    }

    .slider:hover {
      box-shadow: 0 0 8px rgba(76, 175, 80, 0.3);
    }

    input:checked+.slider:hover {
      box-shadow: 0 0 8px rgba(76, 175, 80, 0.5);
    }

    .comparison-notes {
      position: fixed;
      top: 20px;
      right: 20px;
      background: white;
      padding: 15px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      max-width: 250px;
      font-size: 12px;
    }

    .comparison-notes h3 {
      margin: 0 0 10px 0;
      color: #1a73e8;
      font-size: 14px;
    }

    .improvement-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      color: #137333;
    }

    .improvement-item::before {
      content: "✓";
      margin-right: 6px;
      font-weight: bold;
    }

    .form-group {
      margin-bottom: 12px;
    }

    label {
      display: block;
      margin-bottom: 4px;
      font-weight: 500;
      color: #555;
    }

    input[type="text"] {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 13px;
      box-sizing: border-box;
    }

    .btn {
      width: 100%;
      padding: 8px 12px;
      background: #1a73e8;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 13px;
      cursor: pointer;
      margin-top: 8px;
    }

    .btn:hover {
      background: #1557b0;
    }

    .info {
      background: #e8f0fe;
      border: 1px solid #1a73e8;
      border-radius: 4px;
      padding: 10px;
      margin-top: 12px;
      font-size: 12px;
      color: #1a73e8;
    }
  </style>
</head>

<body>
  <div class="comparison-notes">
    <h3>🎨 设计改进</h3>
    <div class="improvement-item">统一的开关设计风格</div>
    <div class="improvement-item">更直观的开/关状态</div>
    <div class="improvement-item">平滑的动画过渡</div>
    <div class="improvement-item">更好的视觉层次</div>
    <div class="improvement-item">与AI功能开关一致</div>
    <div class="improvement-item">现代化的交互体验</div>
  </div>

  <div class="demo-container">
    <!-- 原始设计 -->
    <div class="demo-section">
      <div class="demo-title original">原始复选框设计</div>
      <div class="container">
        <h1>Save to Flomo - 设置</h1>

        <div class="section">
          <div class="section-title">Flomo 配置</div>
          <div class="form-group">
            <label for="api-url-1">Flomo API 地址</label>
            <input type="text" id="api-url-1" placeholder="例如: https://flomoapp.com/api/...">
          </div>
        </div>

        <div class="section">
          <div class="section-title">其他设置</div>
          <div class="checkbox-group">
            <input type="checkbox" id="auto-close-1" checked>
            <label for="auto-close-1">保存成功后自动关闭侧边栏</label>
          </div>
        </div>

        <button class="btn">保存设置</button>

        <div class="info">
          💡 使用传统的方形复选框设计
        </div>
      </div>
    </div>

    <!-- 改进设计 -->
    <div class="demo-section">
      <div class="demo-title improved">开关样式设计</div>
      <div class="container">
        <h1>Save to Flomo - 设置</h1>

        <div class="section">
          <div class="section-title">Flomo 配置</div>
          <div class="form-group">
            <label for="api-url-2">Flomo API 地址</label>
            <input type="text" id="api-url-2" placeholder="例如: https://flomoapp.com/api/...">
          </div>
        </div>

        <div class="section">
          <div class="section-title">其他设置</div>
          <div class="toggle-switch">
            <div class="toggle-switch-info">
              <div class="toggle-switch-label">自动关闭侧边栏</div>
              <div class="toggle-switch-description">保存成功后自动关闭侧边栏</div>
            </div>
            <label class="switch">
              <input type="checkbox" id="auto-close-2" checked>
              <span class="slider"></span>
            </label>
          </div>
        </div>

        <button class="btn">保存设置</button>

        <div class="info">
          💡 使用现代化的开关样式设计，与AI功能保持一致
        </div>
      </div>
    </div>
  </div>
</body>

</html>
